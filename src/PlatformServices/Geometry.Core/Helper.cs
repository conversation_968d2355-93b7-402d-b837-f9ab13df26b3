namespace Geometry.Core
{
    public static class Helper
    {
        /// <summary>
        /// Default boundary tolerance used to handle floating-point precision issues when testing points near polygon boundaries.
        /// </summary>
        private const double DEFAULT_BOUNDARY_TOLERANCE = 0.0001;

        /// <summary>
        /// Determines whether a point is inside a polygon using the ray casting algorithm.
        /// </summary>
        /// <param name="polygon">The polygon vertices in order. Must contain at least 3 vertices.</param>
        /// <param name="testPoint">The point to test for inclusion in the polygon.</param>
        /// <param name="boundaryTolerance">
        /// A small value used to handle floating-point precision issues near polygon boundaries.
        /// Points exactly on the boundary are treated as outside the polygon.
        /// </param>
        /// <returns>
        /// <c>true</c> if the point is inside the polygon; <c>false</c> if the point is outside or on the boundary.
        /// </returns>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="polygon"/> is null.</exception>
        /// <exception cref="ArgumentException">Thrown when <paramref name="polygon"/> has fewer than 3 vertices.</exception>
        /// <remarks>
        /// This method implements the ray casting algorithm (also known as the "even-odd rule").
        /// It casts a horizontal ray from the test point to the right and counts how many times
        /// it intersects the polygon's edges. If the count is odd, the point is inside; if even, it's outside.
        ///
        /// The boundary tolerance parameter is used to handle edge cases where points lie exactly
        /// on polygon edges or vertices, treating them as outside the polygon.
        /// </remarks>
        public static bool IsPointInPolygon(List<PointD> polygon, PointD testPoint, double boundaryTolerance = DEFAULT_BOUNDARY_TOLERANCE)
        {
            if (polygon == null)
                throw new ArgumentNullException(nameof(polygon));

            if (polygon.Count < 3)
                throw new ArgumentException("Polygon must have at least 3 vertices.", nameof(polygon));

            bool isInsidePolygon = false;

            for (int currentVertexIndex = 0, previousVertexIndex = polygon.Count - 1;
                 currentVertexIndex < polygon.Count;
                 previousVertexIndex = currentVertexIndex++)
            {
                var currentVertex = polygon[currentVertexIndex];
                var previousVertex = polygon[previousVertexIndex];

                if (DoesEdgeCrossHorizontalRay(
                    currentVertex, previousVertex, testPoint, boundaryTolerance))
                {
                    isInsidePolygon = !isInsidePolygon;
                }
            }

            return isInsidePolygon;
        }

        /// <summary>
        /// Determines whether an edge of the polygon crosses a horizontal ray cast from the test point to the right.
        /// </summary>
        /// <param name="vertex1">The first vertex of the edge.</param>
        /// <param name="vertex2">The second vertex of the edge.</param>
        /// <param name="testPoint">The point from which the horizontal ray is cast.</param>
        /// <param name="boundaryTolerance">Tolerance for handling points near the boundary (currently unused for backward compatibility).</param>
        /// <returns>
        /// <c>true</c> if the edge crosses the horizontal ray to the right of the test point; otherwise, <c>false</c>.
        /// </returns>
        private static bool DoesEdgeCrossHorizontalRay(
            PointD vertex1, PointD vertex2, PointD testPoint, double boundaryTolerance)
        {
            // Check if edge crosses horizontal line through test point
            bool edgeCrossesHorizontally = (vertex1.Y > testPoint.Y) != (vertex2.Y > testPoint.Y);

            if (!edgeCrossesHorizontally)
                return false;

            // Avoid division by zero for horizontal edges
            if (Math.Abs(vertex2.Y - vertex1.Y) < double.Epsilon)
                return false;

            // Calculate intersection X coordinate using linear interpolation
            double intersectionX = (vertex2.X - vertex1.X) * (testPoint.Y - vertex1.Y) /
                                  (vertex2.Y - vertex1.Y) + vertex1.X;

            // Return true if intersection is to the right of test point
            return testPoint.X < intersectionX;
        }

        public static List<double> FindXDoesIntersectPolygon(double y, List<PointD> polygon)
        {
            var intersectingPoints = new List<double>();

            var prevIndex = polygon.Count - 1;

            for (int i = 0; i < polygon.Count; i++)
            {
                var currentIndex = i;

                var startX = polygon[prevIndex].X;
                var startY = polygon[prevIndex].Y;
                var endX = polygon[currentIndex].X;
                var endY = polygon[currentIndex].Y;

                // Check if the line segment intersects with the horizontal line at coordinate y
                if ((startY <= y && endY >= y) || (startY >= y && endY <= y))
                {
                    // Calculate the x-coordinate of the intersection point using linear interpolation
                    var intersectX = startX + (y - startY) * (endX - startX) / (endY - startY);

                    // Add the intersecting point if it is not already present in the list
                    if (!intersectingPoints.Exists(p => p == intersectX))
                    {
                        intersectingPoints.Add(intersectX);
                    }
                }

                prevIndex = currentIndex;
            }

            return intersectingPoints;
        }

        public static bool CheckIfLineIntersectsPolygon(PointD start, PointD end, List<PointD> polygon)
        {
            if (!IsPointInPolygon(polygon, start) || !IsPointInPolygon(polygon, end))
            {
                return true;
            }

            // Calculate the distance between start and end points
            var distance = Math.Sqrt(Math.Pow(end.X - start.X, 2) + Math.Pow(end.Y - start.Y, 2));

            // Divide the distance by the desired density of points to check
            var steps = (int)Math.Round(distance / 0.01);

            // Generate and check each point on the line for intersection with the polygon
            for (int i = 0; i <= steps; i++)
            {
                var ratio = (double)i / steps;
                var pointOnLine = new PointD((1 - ratio) * start.X + ratio * end.X, (1 - ratio) * start.Y + ratio * end.Y);

                if (!IsPointInPolygon(polygon, pointOnLine))
                {
                    return true;
                }
            }

            return false;
        }

        public static double FindYOnPolygonEdge(double x, List<PointD> polygon)
        {
            int i, j;
            var possibilitiesOfY = new List<double>();

            for (i = 0, j = polygon.Count - 1; i < polygon.Count; j = i++)
            {
                var startX = polygon[j].X;
                var startY = polygon[j].Y;
                var endX = polygon[i].X;
                var endY = polygon[i].Y;

                if ((x >= startX && x <= endX) || (x <= startX && x >= endX))
                {
                    var slope = (endY - startY) / (endX - startX);
                    var y = startY + slope * (x - startX);

                    possibilitiesOfY.Add(y);
                }
            }

            if (possibilitiesOfY.Any())
            {
                return possibilitiesOfY.Max();
            }

            return polygon.OrderBy(x => x.X).ThenByDescending(x => x.Y).First().Y;
        }

        public static double GetInstrumentXPoint(
            PointD referenceOriginPoint,
            PointD sectionUpstreamPoint,
            PointD sectionDownstreamPoint,
            PointD sectionMidpoint,
            PointD instrumentPoint)
        {
            var upstreamToMidpointInstrumentProjection =
                CalculateIntersectionPoint(
                    instrumentPoint,
                    sectionUpstreamPoint,
                    sectionMidpoint);

            var distanceFromUpstreamToUpstreamProjection =
                upstreamToMidpointInstrumentProjection.GetDistance(
                    sectionUpstreamPoint);

            var midpointToDownstreamInstrumentProjection =
                CalculateIntersectionPoint(
                    instrumentPoint,
                    sectionMidpoint,
                    sectionDownstreamPoint);

            var distanceFromDownstreamProjectionToDownstream =
                midpointToDownstreamInstrumentProjection.GetDistance(
                    sectionDownstreamPoint);

            var distanceFromUpstreamToMidpoint =
                sectionUpstreamPoint.GetDistance(sectionMidpoint);

            var distanceFromDownstreamToMidpoint =
                sectionDownstreamPoint.GetDistance(sectionMidpoint);

            /*
             * True: The instrument's projection onto the upstream-midpoint segment falls between the upstream point and midpoint
             * False: The instrument's projection extends beyond the midpoint (closer to or past the midpoint)
             */
            var upstreamProjectionIsCloserToUpstreamThanMidpoint =
                distanceFromUpstreamToUpstreamProjection < distanceFromUpstreamToMidpoint;

            /*
             * True: The instrument's projection onto the midpoint-downstream segment extends beyond the downstream point
             * False: The instrument's projection falls between the midpoint and downstream point
             */
            var downstreamProjectionIsFartherFromDownstreamThanMidpoint =
                distanceFromDownstreamProjectionToDownstream > distanceFromDownstreamToMidpoint;

            if (!upstreamProjectionIsCloserToUpstreamThanMidpoint &&
                downstreamProjectionIsFartherFromDownstreamThanMidpoint)
            {
                // Instrument point is the midpoint
                return distanceFromUpstreamToMidpoint + referenceOriginPoint.X;
            }

            if (!downstreamProjectionIsFartherFromDownstreamThanMidpoint)
            {
                // Instrument point is located between midpoint and downstream
                var distanceFromMidpointToInstrument =
                    sectionMidpoint.GetDistance(instrumentPoint);

                return distanceFromMidpointToInstrument +
                       distanceFromUpstreamToMidpoint + 
                       referenceOriginPoint.X;
            }

            // Instrument point is located between upstream and midpoint
            return distanceFromUpstreamToUpstreamProjection +
                   referenceOriginPoint.X;
        }

        public static double GetInstrumentXPoint(
            PointD referenceOriginPoint,
            PointD sectionUpstreamPoint,
            PointD sectionDownstreamPoint,
            PointD instrumentCoordinate)
        {
            var perpendicularPoint = CalculateIntersectionPoint(instrumentCoordinate, sectionUpstreamPoint, sectionDownstreamPoint);

            var distance = perpendicularPoint.GetDistance(sectionUpstreamPoint);

            return distance + referenceOriginPoint.X;
        }

        private static PointD CalculateIntersectionPoint(PointD refPoint, PointD point1, PointD point2)
        {
            var a = (point2.Y - point1.Y) / (point2.X - point1.X);
            var b = ((point1.Y * point2.X) - (point1.X * point2.Y)) / (point2.X - point1.X);

            var m = -1 / a;
            var n = (refPoint.Y + (((point2.X - point1.X) / (point2.Y - point1.Y)) * refPoint.X));

            var xA = (n - b) / (a - m);
            var yA = a * xA + b;

            xA = double.IsNaN(xA) ? 0 : xA;
            yA = double.IsNaN(yA) ? 0 : yA;

            return new(xA, yA);
        }
    }
}