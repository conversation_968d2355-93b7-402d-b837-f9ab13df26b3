namespace Geometry.Core
{
    public static class Helper
    {
        public static bool IsPointInPolygon(List<PointD> polygon, PointD point, double delta = 0.0001)
        {
            int i, j;
            bool result = false;

            for (i = 0, j = polygon.Count - 1; i < polygon.Count; j = i++)
            {
                // Slightly reducing the coordinates of the vertices of the polygon 
                var xi = polygon[i].X + delta;
                var yi = polygon[i].Y + delta;
                var xj = polygon[j].X + delta;
                var yj = polygon[j].Y + delta;

                if ((yi > point.Y) != (yj > point.Y) &&
                    (point.X < (xj - xi) * (point.Y - yi) / (yj - yi) + xi))
                {
                    result = !result;
                }
            }

            return result;
        }

        public static List<double> FindXDoesIntersectPolygon(double y, List<PointD> polygon)
        {
            var intersectingPoints = new List<double>();

            var prevIndex = polygon.Count - 1;

            for (int i = 0; i < polygon.Count; i++)
            {
                var currentIndex = i;

                var startX = polygon[prevIndex].X;
                var startY = polygon[prevIndex].Y;
                var endX = polygon[currentIndex].X;
                var endY = polygon[currentIndex].Y;

                // Check if the line segment intersects with the horizontal line at coordinate y
                if ((startY <= y && endY >= y) || (startY >= y && endY <= y))
                {
                    // Calculate the x-coordinate of the intersection point using linear interpolation
                    var intersectX = startX + (y - startY) * (endX - startX) / (endY - startY);

                    // Add the intersecting point if it is not already present in the list
                    if (!intersectingPoints.Exists(p => p == intersectX))
                    {
                        intersectingPoints.Add(intersectX);
                    }
                }

                prevIndex = currentIndex;
            }

            return intersectingPoints;
        }

        public static bool CheckIfLineIntersectsPolygon(PointD start, PointD end, List<PointD> polygon)
        {
            if (!IsPointInPolygon(polygon, start) || !IsPointInPolygon(polygon, end))
            {
                return true;
            }

            // Calculate the distance between start and end points
            var distance = Math.Sqrt(Math.Pow(end.X - start.X, 2) + Math.Pow(end.Y - start.Y, 2));

            // Divide the distance by the desired density of points to check
            var steps = (int)Math.Round(distance / 0.01);

            // Generate and check each point on the line for intersection with the polygon
            for (int i = 0; i <= steps; i++)
            {
                var ratio = (double)i / steps;
                var pointOnLine = new PointD((1 - ratio) * start.X + ratio * end.X, (1 - ratio) * start.Y + ratio * end.Y);

                if (!IsPointInPolygon(polygon, pointOnLine))
                {
                    return true;
                }
            }

            return false;
        }

        public static double FindYOnPolygonEdge(double x, List<PointD> polygon)
        {
            int i, j;
            var possibilitiesOfY = new List<double>();

            for (i = 0, j = polygon.Count - 1; i < polygon.Count; j = i++)
            {
                var startX = polygon[j].X;
                var startY = polygon[j].Y;
                var endX = polygon[i].X;
                var endY = polygon[i].Y;

                if ((x >= startX && x <= endX) || (x <= startX && x >= endX))
                {
                    var slope = (endY - startY) / (endX - startX);
                    var y = startY + slope * (x - startX);

                    possibilitiesOfY.Add(y);
                }
            }

            if (possibilitiesOfY.Any())
            {
                return possibilitiesOfY.Max();
            }

            return polygon.OrderBy(x => x.X).ThenByDescending(x => x.Y).First().Y;
        }

        public static double GetInstrumentXPoint(
            PointD referenceOriginPoint,
            PointD sectionUpstreamPoint,
            PointD sectionDownstreamPoint,
            PointD sectionMidpoint,
            PointD instrumentPoint)
        {
            var upstreamToMidpointInstrumentProjection =
                CalculateIntersectionPoint(
                    instrumentPoint,
                    sectionUpstreamPoint,
                    sectionMidpoint);

            var distanceFromUpstreamToUpstreamProjection =
                upstreamToMidpointInstrumentProjection.GetDistance(
                    sectionUpstreamPoint);

            var midpointToDownstreamInstrumentProjection =
                CalculateIntersectionPoint(
                    instrumentPoint,
                    sectionMidpoint,
                    sectionDownstreamPoint);

            var distanceFromDownstreamProjectionToDownstream =
                midpointToDownstreamInstrumentProjection.GetDistance(
                    sectionDownstreamPoint);

            var distanceFromUpstreamToMidpoint =
                sectionUpstreamPoint.GetDistance(sectionMidpoint);

            var distanceFromDownstreamToMidpoint =
                sectionDownstreamPoint.GetDistance(sectionMidpoint);

            /*
             * True: The instrument's projection onto the upstream-midpoint segment falls between the upstream point and midpoint
             * False: The instrument's projection extends beyond the midpoint (closer to or past the midpoint)
             */
            var upstreamProjectionIsCloserToUpstreamThanMidpoint =
                distanceFromUpstreamToUpstreamProjection < distanceFromUpstreamToMidpoint;

            /*
             * True: The instrument's projection onto the midpoint-downstream segment extends beyond the downstream point
             * False: The instrument's projection falls between the midpoint and downstream point
             */
            var downstreamProjectionIsFartherFromDownstreamThanMidpoint =
                distanceFromDownstreamProjectionToDownstream > distanceFromDownstreamToMidpoint;

            if (!upstreamProjectionIsCloserToUpstreamThanMidpoint &&
                downstreamProjectionIsFartherFromDownstreamThanMidpoint)
            {
                // Instrument point is the midpoint
                return distanceFromUpstreamToMidpoint + referenceOriginPoint.X;
            }

            if (!downstreamProjectionIsFartherFromDownstreamThanMidpoint)
            {
                // Instrument point is located between midpoint and downstream
                var distanceFromMidpointToInstrument =
                    sectionMidpoint.GetDistance(instrumentPoint);

                return distanceFromMidpointToInstrument +
                       distanceFromUpstreamToMidpoint + 
                       referenceOriginPoint.X;
            }

            // Instrument point is located between upstream and midpoint
            return distanceFromUpstreamToUpstreamProjection +
                   referenceOriginPoint.X;
        }

        public static double GetInstrumentXPoint(
            PointD referenceOriginPoint,
            PointD sectionUpstreamPoint,
            PointD sectionDownstreamPoint,
            PointD instrumentCoordinate)
        {
            var perpendicularPoint = CalculateIntersectionPoint(instrumentCoordinate, sectionUpstreamPoint, sectionDownstreamPoint);

            var distance = perpendicularPoint.GetDistance(sectionUpstreamPoint);

            return distance + referenceOriginPoint.X;
        }

        private static PointD CalculateIntersectionPoint(PointD refPoint, PointD point1, PointD point2)
        {
            var a = (point2.Y - point1.Y) / (point2.X - point1.X);
            var b = ((point1.Y * point2.X) - (point1.X * point2.Y)) / (point2.X - point1.X);

            var m = -1 / a;
            var n = (refPoint.Y + (((point2.X - point1.X) / (point2.Y - point1.Y)) * refPoint.X));

            var xA = (n - b) / (a - m);
            var yA = a * xA + b;

            xA = double.IsNaN(xA) ? 0 : xA;
            yA = double.IsNaN(yA) ? 0 : yA;

            return new(xA, yA);
        }
    }
}