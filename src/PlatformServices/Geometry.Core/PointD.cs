using System.Drawing;

namespace Geometry.Core
{
    public struct PointD
    {
        public double X;
        public double Y;

        public PointD(double x, double y)
        {
            X = x;
            Y = y;
        }

        public readonly Point ToPoint()
        {
            return new Point((int)X, (int)Y);
        }

        public override readonly bool Equals(object obj)
        {
            return obj is PointD d && this == d;
        }

        public override readonly int GetHashCode()
        {
            return X.GetHashCode() ^ Y.GetHashCode();
        }

        public static bool operator ==(PointD a, PointD b)
        {
            return a.X == b.X && a.Y == b.Y;
        }

        public static bool operator !=(PointD a, PointD b)
        {
            return !(a == b);
        }
        
        public void ApplyRounding()
        {
            X = Math.Round(X, 3, MidpointRounding.AwayFromZero);
            Y = Math.Round(Y, 3, MidpointRounding.AwayFromZero);
        }
        
        public double GetXAxisDistance(PointD other)
        {
            return Math.Abs(this.X - other.X);
        }
        
        public double GetDistance(PointD other)
        {
            return Math.Sqrt(Math.Pow(this.X - other.X, 2) + Math.Pow(this.Y - other.Y, 2));
        }
    }
}
