<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Bogus" Version="34.0.2" />
        <PackageReference Include="FluentAssertions" Version="6.3.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.1.0"/>
        <PackageReference Include="Moq" Version="4.18.4" />
        <PackageReference Include="Moq.AutoMock" Version="3.4.0" />
        <PackageReference Include="xunit" Version="2.4.1"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="3.1.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Geometry.Core\Geometry.Core.csproj" />
    </ItemGroup>

</Project>
