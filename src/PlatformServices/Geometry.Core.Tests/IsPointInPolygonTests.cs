using Geometry.Core;

namespace Geometry.Core.Tests;

[Trait("Helper", "IsPointInPolygon")]
public class IsPointInPolygonTests
{
    private readonly Faker _faker = new();

    [Fact(DisplayName = "When polygon is null, then should throw ArgumentNullException")]
    public void WhenPolygonIsNull_ShouldThrowArgumentNullException()
    {
        // Arrange
        List<PointD>? polygon = null;
        var testPoint = new PointD(0, 0);

        // Act & Assert
        var exception = Assert.Throws<ArgumentNullException>(() => 
            Helper.IsPointInPolygon(polygon!, testPoint));
        
        exception.ParamName.Should().Be("polygon");
    }

    [Fact(DisplayName = "When polygon is empty, then should throw ArgumentException")]
    public void WhenPolygonIsEmpty_ShouldThrowArgumentException()
    {
        // Arrange
        var polygon = new List<PointD>();
        var testPoint = new PointD(0, 0);

        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => 
            Helper.IsPointInPolygon(polygon, testPoint));
        
        exception.ParamName.Should().Be("polygon");
        exception.Message.Should().Contain("at least 3 vertices");
    }

    [Theory(DisplayName = "When polygon has insufficient vertices, then should throw ArgumentException")]
    [InlineData(1)]
    [InlineData(2)]
    public void WhenPolygonHasInsufficientVertices_ShouldThrowArgumentException(int vertexCount)
    {
        // Arrange
        var polygon = GenerateRandomPolygon(vertexCount);
        var testPoint = new PointD(0, 0);

        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => 
            Helper.IsPointInPolygon(polygon, testPoint));
        
        exception.ParamName.Should().Be("polygon");
        exception.Message.Should().Contain("at least 3 vertices");
    }

    [Fact(DisplayName = "When point is clearly inside square polygon, then should return true")]
    public void WhenPointIsClearlyInsideSquarePolygon_ShouldReturnTrue()
    {
        // Arrange
        var polygon = CreateSquarePolygon(0, 0, 10);
        var testPoint = new PointD(5, 5); // Center of square

        // Act
        var result = Helper.IsPointInPolygon(polygon, testPoint);

        // Assert
        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point is clearly outside square polygon, then should return false")]
    public void WhenPointIsClearlyOutsideSquarePolygon_ShouldReturnFalse()
    {
        // Arrange
        var polygon = CreateSquarePolygon(0, 0, 10);
        var testPoint = new PointD(15, 15); // Outside square

        // Act
        var result = Helper.IsPointInPolygon(polygon, testPoint);

        // Assert
        result.Should().BeFalse();
    }

    [Theory(DisplayName = "When point is on polygon vertex, then behavior depends on boundary tolerance")]
    [InlineData(0, 0)]
    [InlineData(10, 0)]
    [InlineData(10, 10)]
    [InlineData(0, 10)]
    public void WhenPointIsOnPolygonVertex_BehaviorDependsOnBoundaryTolerance(double x, double y)
    {
        // Arrange
        var polygon = CreateSquarePolygon(0, 0, 10);
        var testPoint = new PointD(x, y);

        // Act
        var result = Helper.IsPointInPolygon(polygon, testPoint);

        // Assert - The result may vary based on boundary tolerance implementation
        // This test verifies the method doesn't crash and returns a consistent result
        var secondResult = Helper.IsPointInPolygon(polygon, testPoint);
        result.Should().Be(secondResult, "result should be consistent for the same input");
    }

    [Theory(DisplayName = "When point is on polygon edge, then behavior depends on boundary tolerance")]
    [InlineData(5, 0)]   // Bottom edge
    [InlineData(10, 5)]  // Right edge
    [InlineData(5, 10)]  // Top edge
    [InlineData(0, 5)]   // Left edge
    public void WhenPointIsOnPolygonEdge_BehaviorDependsOnBoundaryTolerance(double x, double y)
    {
        // Arrange
        var polygon = CreateSquarePolygon(0, 0, 10);
        var testPoint = new PointD(x, y);

        // Act
        var result = Helper.IsPointInPolygon(polygon, testPoint);

        // Assert - The result may vary based on boundary tolerance implementation
        // This test verifies the method doesn't crash and returns a consistent result
        var secondResult = Helper.IsPointInPolygon(polygon, testPoint);
        result.Should().Be(secondResult, "result should be consistent for the same input");
    }

    [Fact(DisplayName = "When point is at polygon centroid, then should return true")]
    public void WhenPointIsAtPolygonCentroid_ShouldReturnTrue()
    {
        // Arrange
        var polygon = CreateTrianglePolygon();
        var centroid = CalculateCentroid(polygon);

        // Act
        var result = Helper.IsPointInPolygon(polygon, centroid);

        // Assert
        result.Should().BeTrue();
    }

    [Theory(DisplayName = "When using different boundary tolerance values, then should produce consistent results for interior points")]
    [InlineData(0.0001)]
    [InlineData(0.001)]
    [InlineData(0.01)]
    public void WhenUsingDifferentBoundaryToleranceValues_ShouldProduceConsistentResultsForInteriorPoints(double tolerance)
    {
        // Arrange
        var polygon = CreateSquarePolygon(0, 0, 10);
        var interiorPoint = new PointD(5, 5); // Well inside the polygon

        // Act
        var result = Helper.IsPointInPolygon(polygon, interiorPoint, tolerance);

        // Assert
        result.Should().BeTrue("interior points should always be inside regardless of tolerance");
    }

    [Fact(DisplayName = "When polygon is complex with concave section, then should handle correctly")]
    public void WhenPolygonIsComplexWithConcaveSection_ShouldHandleCorrectly()
    {
        // Arrange - Create an L-shaped polygon (concave)
        var polygon = CreateLShapedPolygon();
        var insidePoint = new PointD(1, 1);   // Inside the L
        var outsidePoint = new PointD(3.5, 3.5);  // In the concave area (outside)

        // Act
        var insideResult = Helper.IsPointInPolygon(polygon, insidePoint);
        var outsideResult = Helper.IsPointInPolygon(polygon, outsidePoint);

        // Assert
        insideResult.Should().BeTrue();
        outsideResult.Should().BeFalse();
    }

    [Fact(DisplayName = "When coordinates are very large, then should maintain precision")]
    public void WhenCoordinatesAreVeryLarge_ShouldMaintainPrecision()
    {
        // Arrange
        const double offset = 1_000_000;
        var polygon = CreateSquarePolygon(offset, offset, 10);
        var insidePoint = new PointD(offset + 5, offset + 5);
        var outsidePoint = new PointD(offset + 15, offset + 15);

        // Act
        var insideResult = Helper.IsPointInPolygon(polygon, insidePoint);
        var outsideResult = Helper.IsPointInPolygon(polygon, outsidePoint);

        // Assert
        insideResult.Should().BeTrue();
        outsideResult.Should().BeFalse();
    }

    [Fact(DisplayName = "When polygon has many vertices, then should handle efficiently")]
    public void WhenPolygonHasManyVertices_ShouldHandleEfficiently()
    {
        // Arrange
        var polygon = CreateCircularPolygon(center: new PointD(0, 0), radius: 10, vertexCount: 100);
        var insidePoint = new PointD(0, 0);   // Center
        var outsidePoint = new PointD(15, 0); // Outside circle

        // Act
        var insideResult = Helper.IsPointInPolygon(polygon, insidePoint);
        var outsideResult = Helper.IsPointInPolygon(polygon, outsidePoint);

        // Assert
        insideResult.Should().BeTrue();
        outsideResult.Should().BeFalse();
    }

    [Fact(DisplayName = "When testing multiple random points, then should produce consistent results")]
    public void WhenTestingMultipleRandomPoints_ShouldProduceConsistentResults()
    {
        // Arrange
        var polygon = CreateSquarePolygon(0, 0, 10);
        var testPoints = GenerateRandomTestPoints(50);

        // Act & Assert
        foreach (var point in testPoints)
        {
            var result = Helper.IsPointInPolygon(polygon, point);
            
            // Verify consistency by testing the same point multiple times
            for (int i = 0; i < 3; i++)
            {
                var repeatResult = Helper.IsPointInPolygon(polygon, point);
                repeatResult.Should().Be(result, $"result should be consistent for point ({point.X}, {point.Y})");
            }
        }
    }

    #region Helper Methods

    private List<PointD> CreateSquarePolygon(double x, double y, double size)
    {
        return new List<PointD>
        {
            new(x, y),
            new(x + size, y),
            new(x + size, y + size),
            new(x, y + size)
        };
    }

    private List<PointD> CreateTrianglePolygon()
    {
        return new List<PointD>
        {
            new(0, 0),
            new(10, 0),
            new(5, 10)
        };
    }

    private List<PointD> CreateLShapedPolygon()
    {
        return new List<PointD>
        {
            new(0, 0),
            new(3, 0),
            new(3, 2),
            new(5, 2),
            new(5, 5),
            new(0, 5)
        };
    }

    private List<PointD> CreateCircularPolygon(PointD center, double radius, int vertexCount)
    {
        var polygon = new List<PointD>();
        for (int i = 0; i < vertexCount; i++)
        {
            var angle = 2 * Math.PI * i / vertexCount;
            var x = center.X + radius * Math.Cos(angle);
            var y = center.Y + radius * Math.Sin(angle);
            polygon.Add(new PointD(x, y));
        }
        return polygon;
    }

    private List<PointD> GenerateRandomPolygon(int vertexCount)
    {
        var polygon = new List<PointD>();
        for (int i = 0; i < vertexCount; i++)
        {
            polygon.Add(new PointD(_faker.Random.Double(-100, 100), _faker.Random.Double(-100, 100)));
        }
        return polygon;
    }

    private List<PointD> GenerateRandomTestPoints(int count)
    {
        var points = new List<PointD>();
        for (int i = 0; i < count; i++)
        {
            points.Add(new PointD(_faker.Random.Double(-20, 30), _faker.Random.Double(-20, 30)));
        }
        return points;
    }

    private PointD CalculateCentroid(List<PointD> polygon)
    {
        var sumX = polygon.Sum(p => p.X);
        var sumY = polygon.Sum(p => p.Y);
        return new PointD(sumX / polygon.Count, sumY / polygon.Count);
    }

    #endregion
}
